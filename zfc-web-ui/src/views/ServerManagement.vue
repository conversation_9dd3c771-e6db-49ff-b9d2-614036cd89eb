<template>
  <div class="server-management">
    <div class="header">
      <h2>Server Management</h2>
      <div class="header-actions">
        <el-button
          @click="toggleSearchExpanded"
          :icon="searchExpanded ? ArrowUp : ArrowDown"
          type="default"
          class="search-toggle-btn"
          :aria-expanded="searchExpanded"
          :aria-controls="'search-form-container'"
          :title="searchExpanded ? 'Hide search filters' : 'Show search filters'"
        >
          <span class="search-toggle-text">
            {{ searchExpanded ? 'Hide Search' : 'Show Search' }}
          </span>
        </el-button>
        <el-button type="primary" @click="showAddDialog = true">
          Add Server
        </el-button>
      </div>
    </div>

    <!-- Collapsible Search Form -->
    <el-collapse-transition>
      <div
        v-show="searchExpanded"
        class="search-container"
        id="search-form-container"
        role="region"
        aria-label="Search and filter options"
      >
        <el-form :model="searchForm" class="search-form" :inline="true">
          <el-form-item label="ID">
            <el-input
              v-model="searchForm.id"
              placeholder="Search by exact ID"
              clearable
              style="width: 120px"
              @input="handleSearchInput"
            />
          </el-form-item>
          <el-form-item label="Name">
            <el-input
              v-model="searchForm.name"
              placeholder="Search by name (regex supported)"
              clearable
              style="width: 200px"
              @input="handleSearchInput"
            />
          </el-form-item>
          <el-form-item label="IP Address">
            <el-input
              v-model="searchForm.ipAddr"
              placeholder="Search IP address (regex supported)"
              clearable
              style="width: 200px"
              @input="handleSearchInput"
            />
          </el-form-item>
          <el-form-item label="Version">
            <el-select
              v-model="searchForm.version"
              placeholder="Select versions"
              clearable
              filterable
              multiple
              style="width: 180px"
              @change="handleSearchChange"
            >
              <el-option
                v-for="version in availableVersions"
                :key="version"
                :label="version"
                :value="version"
              />
            </el-select>
          </el-form-item>
          <el-form-item label="Status">
            <el-select
              v-model="searchForm.status"
              placeholder="Select status"
              clearable
              multiple
              style="width: 150px"
              @change="handleSearchChange"
            >
              <el-option
                v-for="status in availableStatuses"
                :key="status"
                :label="status"
                :value="status"
              />
            </el-select>
          </el-form-item>
          <el-form-item>
            <el-button type="primary" @click="handleSearch" :loading="loading">
              Search
            </el-button>
            <el-button @click="handleClearSearch">
              Clear
            </el-button>
          </el-form-item>
        </el-form>
      </div>
    </el-collapse-transition>

    <el-card class="table-card">
      <el-table
        :data="servers"
        v-loading="loading"
        style="width: 100%"
        :header-cell-style="headerCellStyle"
        class="server-table"
      >
        <el-table-column
          prop="id"
          label="ID"
          width="80"
        />
        <el-table-column
          prop="display_name"
          label="Name"
          min-width="120"
        />
        <el-table-column
          prop="ip_addr"
          label="IP Address"
          min-width="140"
        />
        <el-table-column
          prop="version"
          label="Version"
          width="100"
          align="center"
        >
          <template #default="scope">
            <el-tag
              size="small"
              :type="scope.row.version ? 'info' : 'danger'"
              effect="plain"
            >
              {{ scope.row.version || 'Unknown' }}
            </el-tag>
          </template>
        </el-table-column>
        <el-table-column
          prop="is_online"
          label="Status"
          width="100"
          align="center"
        >
          <template #default="scope">
            <el-tag :type="scope.row.is_online ? 'success' : 'danger'">
              {{ scope.row.is_online ? 'Online' : 'Offline' }}
            </el-tag>
          </template>
        </el-table-column>
        <el-table-column
          label="Ports"
          min-width="180"
        >
          <template #default="scope">
            <div class="ports-info">
              <el-popover
                placement="top"
                :width="200"
                trigger="hover"
                popper-class="ports-popover"
              >
                <template #reference>
                  <el-tag
                    type="info"
                    class="ports-count"
                    effect="plain"
                  >
                    {{ scope.row.used_ports?.length || 0 }} / {{ getPortsTotal(scope.row) }} Ports
                  </el-tag>
                </template>
                <template #default>
                  <div class="ports-list">
                    <div class="ports-list-header">Port Details</div>
                    <el-scrollbar max-height="200px">
                      <div 
                        v-for="port in scope.row.used_ports" 
                        :key="port"
                        class="port-item"
                      >
                        Port {{ port }}
                      </div>
                      <div v-if="!scope.row.used_ports?.length" class="port-item">
                        No ports in use
                      </div>
                    </el-scrollbar>
                  </div>
                </template>
              </el-popover>
            </div>
          </template>
        </el-table-column>
        <el-table-column 
          label="Actions" 
          width="140"
          fixed="right"
          align="center"
        >
          <template #default="scope">
            <el-space>
              <el-tooltip 
                content="Copy Installation Command" 
                placement="top"
              >
                <el-button
                  size="small"
                  type="primary"
                  :icon="Document"
                  @click="handleCopyCommand(scope.row)"
                  circle
                />
              </el-tooltip>
              <el-tooltip 
                content="Edit Server" 
                placement="top"
              >
                <el-button
                  size="small"
                  type="warning"
                  :icon="Edit"
                  @click="handleEdit(scope.row)"
                  circle
                />
              </el-tooltip>
              <el-button
                size="small"
                type="danger"
                :icon="Delete"
                @click="handleDelete(scope.row)"
                circle
              />
            </el-space>
          </template>
        </el-table-column>
      </el-table>

      <!-- Pagination Controls -->
      <div class="pagination-container">
        <div class="pagination-info">
          <span>Total {{ totalItems }} items</span>
          <el-select
            v-model="pageSize"
            @change="handlePageSizeChange"
            class="page-size-selector"
            size="small"
          >
            <el-option
              v-for="size in pageSizeOptions"
              :key="size"
              :label="`${size} / page`"
              :value="size"
            />
          </el-select>
        </div>
        <el-pagination
          v-model:current-page="currentPage"
          :page-size="pageSize"
          :total="totalItems"
          :page-count="totalPages"
          layout="prev, pager, next, jumper"
          @current-change="handlePageChange"
          class="pagination-controls"
          small
        />
      </div>
    </el-card>

    <!-- Add Server Dialog -->
    <el-dialog
      v-model="showAddDialog"
      title="Add New Server"
      width="500px"
      :close-on-click-modal="false"
      destroy-on-close
      class="server-dialog"
    >
      <el-form
        ref="formRef"
        :model="form"
        :rules="rules"
        label-position="top"
        size="large"
      >
        <el-form-item 
          label="Server Name" 
          prop="display_name"
          :error="formErrors.display_name"
        >
          <el-input 
            v-model="form.display_name" 
            placeholder="Enter server name"
          />
        </el-form-item>

        <el-form-item 
          label="IP Address" 
          prop="ip_addr"
          :error="formErrors.ip_addr"
        >
          <el-input 
            v-model="form.ip_addr" 
            placeholder="Enter IP address"
          />
        </el-form-item>

        <el-form-item 
          label="Interface Name" 
          prop="interface_name"
          :error="formErrors.interface_name"
        >
          <el-input 
            v-model="form.interface_name"
            placeholder="Enter interface name"
          />
        </el-form-item>

        <el-form-item label="Port Range">
          <el-row :gutter="20">
            <el-col :span="11">
              <el-form-item prop="port_start">
                <el-input-number v-model="form.port_start" :min="1" :max="65535" placeholder="Start port" />
              </el-form-item>
            </el-col>
            <el-col :span="2" class="text-center">
              <span class="text-gray-500">to</span>
            </el-col>
            <el-col :span="11">
              <el-form-item prop="port_end">
                <el-input-number v-model="form.port_end" :min="1" :max="65535" placeholder="End port" />
              </el-form-item>
            </el-col>
          </el-row>
        </el-form-item>

        <div class="advanced-options">
          <div class="advanced-options-header" @click="showAdvanced = !showAdvanced">
            <span>Advanced Options</span>
            <el-icon class="advanced-icon" :class="{ 'is-active': showAdvanced }">
              <arrow-down />
            </el-icon>
          </div>
          <div v-show="showAdvanced" class="advanced-options-content">
                <el-form-item label="Traffic Scale">
                  <el-input-number 
                    v-model="form.traffic_scale" 
                    :min="0.1"
                    :max="10"
                    :precision="2"
                    :step="0.1"
                    placeholder="1.00"
                    controls-position="right"
                    style="width: 160px"
                  />
                </el-form-item>

                <el-form-item label="Allow Forward">
                  <el-switch v-model="form.allow_forward" />
                </el-form-item>

                <el-form-item label="Allow IPv6">
                  <el-switch v-model="form.allow_ipv6" />
                </el-form-item>

                <el-form-item label="Allow Latency Test">
                  <el-switch v-model="form.allow_latency_test" />
                </el-form-item>

                <el-form-item label="Forward Endpoints">
                  <el-select
                    v-model="selectedForwardEndpointIds"
                    multiple
                    placeholder="Select forward endpoints"
                    style="width: 100%"
                    @change="handleForwardEndpointsChange"
                  >
                    <el-option
                      v-for="endpoint in availableEndpoints"
                      :key="endpoint.id"
                      :label="endpoint.display_name"
                      :value="endpoint.id"
                    />
                  </el-select>
                </el-form-item>

                <el-form-item 
                  label="Balance Strategy" 
                  v-if="selectedForwardEndpointIds.length > 1"
                >
                  <el-select v-model="form.balance_strategy" placeholder="Select balance strategy">
                    <el-option label="Best Latency" :value="0" />
                    <el-option label="Fallback" :value="1" />
                    <el-option label="Domain Follow" :value="2" />
                    <el-option label="Round Robin" :value="3" />
                    <el-option label="Random" :value="4" />
                  </el-select>
                </el-form-item>
                <el-form-item v-if="selectedForwardEndpointIds.length > 0" label="Use Forward endpoints as Transport">
                  <el-switch v-model="form.use_forward_as_tun" />
                </el-form-item>
          </div>
        </div>
      </el-form>

      <template #footer>
        <div class="dialog-footer">
          <el-button 
            @click="showAddDialog = false"
            class="cancel-button"
          >
            Cancel
          </el-button>
          <el-button 
            type="primary" 
            @click="handleSubmit" 
            :loading="submitting"
            class="submit-button"
          >
            Create
          </el-button>
        </div>
      </template>
    </el-dialog>

    <!-- Edit Server Dialog -->
    <el-dialog
      v-model="showEditDialog"
      title="Edit Server"
      width="500px"
      :close-on-click-modal="false"
      destroy-on-close
      class="server-dialog"
    >
      <el-form
        ref="editFormRef"
        :model="editForm"
        :rules="rules"
        label-position="top"
        size="large"
      >
        <el-form-item 
          label="Display Name" 
          prop="display_name"
          :error="formErrors.display_name"
        >
          <el-input 
            v-model="editForm.display_name" 
            placeholder="Please enter display name"
          />
        </el-form-item>

        <el-form-item 
          label="IP Address" 
          prop="ip_addr"
          :error="formErrors.ip_addr"
        >
          <el-input 
            v-model="editForm.ip_addr" 
            placeholder="Enter IP address"
          />
        </el-form-item>

        <el-form-item 
          label="Interface Name" 
          prop="interface_name"
          :error="formErrors.interface_name"
        >
          <el-input 
            v-model="editForm.interface_name"
            placeholder="Enter interface name"
          />
        </el-form-item>

        <el-form-item label="Port Range">
          <el-row :gutter="20">
            <el-col :span="11">
              <el-form-item prop="port_start">
                <el-input-number v-model="editForm.port_start" :min="1" :max="65535" placeholder="Start port" />
              </el-form-item>
            </el-col>
            <el-col :span="2" class="text-center">
              <span class="text-gray-500">to</span>
            </el-col>
            <el-col :span="11">
              <el-form-item prop="port_end">
                <el-input-number v-model="editForm.port_end" :min="1" :max="65535" placeholder="End port" />
              </el-form-item>
            </el-col>
          </el-row>
        </el-form-item>

        <div class="advanced-options">
          <div class="advanced-options-header" @click="showEditAdvanced = !showEditAdvanced">
            <span>Advanced Options</span>
            <el-icon class="advanced-icon" :class="{ 'is-active': showEditAdvanced }">
              <arrow-down />
            </el-icon>
          </div>
          <div v-show="showEditAdvanced" class="advanced-options-content">
                <el-form-item label="Traffic Scale">
                  <el-input-number 
                    v-model="editForm.traffic_scale" 
                    :min="0.1"
                    :max="10"
                    :precision="2"
                    :step="0.1"
                    placeholder="1.00"
                    controls-position="right"
                    style="width: 160px"
                  />
                </el-form-item>

                <el-form-item label="Allow Forward">
                  <el-switch v-model="editForm.allow_forward" />
                </el-form-item>

                <el-form-item label="Allow IPv6">
                  <el-switch v-model="editForm.allow_ipv6" />
                </el-form-item>

                <el-form-item label="Allow Latency Test">
                  <el-switch v-model="editForm.allow_latency_test" />
                </el-form-item>

                <el-form-item label="Forward Endpoints">
                  <el-select
                    v-model="selectedEditForwardEndpointIds"
                    multiple
                    placeholder="Select forward endpoints"
                    style="width: 100%"
                    @change="handleEditForwardEndpointsChange"
                  >
                    <el-option
                      v-for="endpoint in availableEndpoints"
                      :key="endpoint.id"
                      :label="endpoint.display_name"
                      :value="endpoint.id"
                    />
                  </el-select>
                </el-form-item>

                <el-form-item 
                  label="Balance Strategy" 
                  v-if="selectedEditForwardEndpointIds.length > 1"
                >
                  <el-select v-model="editForm.balance_strategy" placeholder="Select balance strategy">
                    <el-option label="Best Latency" :value="0" />
                    <el-option label="Fallback" :value="1" />
                    <el-option label="Domain Follow" :value="2" />
                    <el-option label="Round Robin" :value="3" />
                    <el-option label="Random" :value="4" />
                  </el-select>
                  <el-form-item v-if="selectedEditForwardEndpointIds.length > 0" label="Use Forward endpoints as Transport">
                  <el-switch v-model="editForm.use_forward_as_tun" />
                </el-form-item>
                </el-form-item>
          </div>
        </div>
      </el-form>

      <template #footer>
        <div class="dialog-footer">
          <el-button 
            @click="showEditDialog = false"
            class="cancel-button"
          >
            Cancel
          </el-button>
          <el-button 
            type="primary" 
            @click="handleEditSubmit" 
            :loading="submitting"
            class="submit-button"
          >
            Save Changes
          </el-button>
        </div>
      </template>
    </el-dialog>
  </div>
</template>

<script setup>
import { ref, computed, onMounted } from 'vue'
import { storeToRefs } from 'pinia'
import { ElMessage, ElMessageBox } from 'element-plus'
import { getServerList, addServer, removeServer, modifyServer, getForwardEndpoints } from '../api'
import { ArrowDown, ArrowUp, Delete, Document, Edit } from '@element-plus/icons-vue'
import { API_HOST } from '../config'
import { useThemeStore } from '../stores/theme'

// Theme store
const themeStore = useThemeStore()
const { isDark } = storeToRefs(themeStore)

// Computed style for table headers - borderless design
const headerCellStyle = computed(() => ({
  background: isDark.value ? 'var(--theme-fill-dark)' : 'var(--theme-fill-light)',
  color: 'var(--theme-text-primary)',
  fontWeight: '600',
  border: 'none'
}))

const servers = ref([])
const loading = ref(false)
const submitting = ref(false)
const showAddDialog = ref(false)
const showEditDialog = ref(false)
const showAdvanced = ref(false)
const showEditAdvanced = ref(false)
const formRef = ref(null)

// Pagination state
const currentPage = ref(1)
const pageSize = ref(20)
const totalItems = ref(0)
const totalPages = ref(1)
const pageSizeOptions = [20, 50, 100, 200]
const editFormRef = ref(null)
const formErrors = ref({})

// Search state
const searchForm = ref({
  id: '',
  name: '',
  ipAddr: '',
  version: [],
  status: []
})
const searchTimeout = ref(null)

// Search UI state
const searchExpanded = ref(false)

// Available options for multi-select filters
const availableVersions = ref([])
const availableStatuses = ref(['Online', 'Offline'])

// Load search expanded state from localStorage on component mount
const loadSearchExpandedState = () => {
  const saved = localStorage.getItem('server-search-expanded')
  if (saved !== null) {
    searchExpanded.value = JSON.parse(saved)
  }
}

// Save search expanded state to localStorage
const saveSearchExpandedState = () => {
  localStorage.setItem('server-search-expanded', JSON.stringify(searchExpanded.value))
}

// Forward endpoints management
const availableEndpoints = ref([])
const selectedForwardEndpointIds = ref([])
const selectedForwardEndpoints = ref([])
const selectedEditForwardEndpointIds = ref([])
const selectedEditForwardEndpoints = ref([])

const form = ref({
  display_name: '',
  ip_addr: '',
  interface_name: '',
  port_start: 30000,
  port_end: 31000,
  traffic_scale: 1.0,
  allow_forward: false,
  allow_latency_test: false,
  allow_ipv6: false,
  balance_strategy: 0,
  use_forward_as_tun: false,
  forward_endpoints: []
})

const editForm = ref({
  server_id: null,
  display_name: '',
  ip_addr: '',
  interface_name: '',
  port_start: 30000,
  port_end: 31000,
  traffic_scale: 1.0,
  allow_forward: false,
  allow_latency_test: false,
  allow_ipv6: false,
  balance_strategy: 0,
  use_forward_as_tun: false,
  forward_endpoints: []
})

const rules = {
  display_name: [
    { required: true, message: 'Please enter display name', trigger: 'blur' }
  ],
  ip_addr: [
    { required: true, message: 'Please enter IP address', trigger: 'blur' }
  ],
  interface_name: [
    { required: true, message: 'Please enter interface name', trigger: 'blur' }
  ],
  port_start: [
    { required: true, message: 'Please enter start port', trigger: 'blur' },
    { type: 'number', message: 'Port must be a number', trigger: 'blur' }
  ],
  port_end: [
    { required: true, message: 'Please enter end port', trigger: 'blur' },
    { type: 'number', message: 'Port must be a number', trigger: 'blur' }
  ]
}

const handleForwardEndpointsChange = (selectedIds) => {
  selectedForwardEndpointIds.value = selectedIds
  const endpointMap = new Map(availableEndpoints.value.map(ep => [ep.id, ep]))
  selectedForwardEndpoints.value = selectedIds
    .map(id => endpointMap.get(id))
    .filter(Boolean)
  form.value.forward_endpoints = selectedIds
  
  if (selectedIds.length <= 1) {
    form.value.balance_strategy = 0
  }
}

const handleEditForwardEndpointsChange = (selectedIds) => {
  selectedEditForwardEndpointIds.value = selectedIds
  const endpointMap = new Map(availableEndpoints.value.map(ep => [ep.id, ep]))
  selectedEditForwardEndpoints.value = selectedIds
    .map(id => endpointMap.get(id))
    .filter(Boolean)
  editForm.value.forward_endpoints = selectedIds
  
  if (selectedIds.length <= 1) {
    editForm.value.balance_strategy = 0
  }
}

const fetchForwardEndpoints = async () => {
  try {
    const { data } = await getForwardEndpoints()
    if (data && Array.isArray(data.forward_endpoints)) {
      availableEndpoints.value = data.forward_endpoints.map(item => ({
        id: item.id,
        display_name: item.display_name || item.name
      }))
    } else {
      console.warn('No forward endpoints data in response:', data)
      availableEndpoints.value = []
    }
  } catch (error) {
    console.error('Failed to fetch forward endpoints:', error)
    ElMessage.error(error?.response?.data?.message || 'Failed to fetch forward endpoints')
    availableEndpoints.value = []
  }
}

const getPortsTotal = (server) => {
  if (!server.port_start || !server.port_end) return 0
  return server.port_end - server.port_start + 1
}

const resetForm = () => {
  formRef.value?.resetFields()
  form.value = {
    display_name: '',
    ip_addr: '',
    interface_name: '',
    port_start: 30000,
    port_end: 31000,
    traffic_scale: 1.0,
    allow_forward: false,
    allow_latency_test: false,
    allow_ipv6: false,
    balance_strategy: 0,
    use_forward_as_tun: false,
    forward_endpoints: []
  }
}

const handleSubmit = async () => {
  if (!formRef.value) return
  
  try {
    const valid = await formRef.value.validate()
    if (valid) {
      submitting.value = true
      try {
        const submitData = { ...form.value }
        
        if (!submitData.forward_endpoints || submitData.forward_endpoints.length <= 1) {
          delete submitData.balance_strategy
        }

        if (!submitData.forward_endpoints) {
          delete submitData.use_forward_as_tun
        }
        
        await addServer(submitData)
        ElMessage.success('Server added successfully')
        showAddDialog.value = false
        resetForm()
        loadServers(currentPage.value, pageSize.value)
      } catch (error) {
        console.error('Error submitting form:', error)
        ElMessage.error(error.message || 'Failed to add server')
      } finally {
        submitting.value = false
      }
    }
  } catch (error) {
    console.error('Validation error:', error)
  }
}

const handleDelete = async (row) => {
  try {
    await ElMessageBox.confirm(
      'Are you sure you want to delete this server?',
      'Warning',
      {
        confirmButtonText: 'OK',
        cancelButtonText: 'Cancel',
        type: 'warning',
      }
    )
    
    loading.value = true
    await removeServer(row.id)
    ElMessage.success('Server deleted successfully')
    await loadServers(currentPage.value, pageSize.value)
  } catch (error) {
    if (error !== 'cancel') {
      console.error('Error deleting server:', error)
      ElMessage.error(error.message || 'Failed to delete server')
    }
  } finally {
    loading.value = false
  }
}

const handleCopyCommand = async (row) => {
  if (!row.server_pubkey || !row.interface_name) {
    ElMessage.error('Missing server public key or interface name')
    return
  }

  const command = `curl -s ${API_HOST}/worker_setup_script/${row.server_pubkey} | bash -s ${row.interface_name}`
  
  try {
    // 尝试使用现代 Clipboard API
    if (navigator.clipboard && window.isSecureContext) {
      await navigator.clipboard.writeText(command)
    } else {
      // 回退方案：使用传统方法
      const textArea = document.createElement('textarea')
      textArea.value = command
      textArea.style.position = 'fixed'
      textArea.style.left = '-999999px'
      textArea.style.top = '-999999px'
      document.body.appendChild(textArea)
      textArea.focus()
      textArea.select()
      try {
        document.execCommand('copy')
        textArea.remove()
      } catch (err) {
        console.error('Failed to copy text:', err)
        textArea.remove()
        throw new Error('Failed to copy text')
      }
    }
    ElMessage.success('Installation command copied to clipboard')
  } catch (err) {
    console.error('Failed to copy:', err)
    ElMessage.error('Failed to copy command to clipboard')
  }
}

const handleEdit = (row) => {
  editForm.value = {
    server_id: row.id,
    display_name: row.display_name,
    ip_addr: row.ip_addr,
    interface_name: row.interface_name,
    port_start: row.port_start,
    port_end: row.port_end,
    traffic_scale: row.traffic_scale,
    allow_forward: row.allow_forward,
    allow_latency_test: row.allow_latency_test,
    allow_ipv6: row.allow_ipv6,
    balance_strategy: row.balance_strategy,
    forward_endpoints: row.forward_endpoints,
    use_forward_as_tun: row.use_forward_as_tun,
  }
  selectedEditForwardEndpointIds.value = row.forward_endpoints;
  selectedEditForwardEndpoints.value = row.forward_endpoints.map(id => availableEndpoints.value.find(item => item.id == id).display_name)
  showEditDialog.value = true
}

const handleEditSubmit = async () => {
  if (!editFormRef.value) return

  try {
    const valid = await editFormRef.value.validate()
    if (valid) {
      submitting.value = true
      try {
        const submitData = {
          server_id: editForm.value.server_id
        }

        if (editForm.value.display_name !== undefined) {
          submitData.display_name = editForm.value.display_name
        }
        if (editForm.value.ip_addr !== undefined) {
          submitData.ip_addr = editForm.value.ip_addr
        }
        if (editForm.value.interface_name !== undefined) {
          submitData.interface_name = editForm.value.interface_name
        }
        if (editForm.value.port_start !== undefined) {
          submitData.port_start = editForm.value.port_start
        }
        if (editForm.value.port_end !== undefined) {
          submitData.port_end = editForm.value.port_end
        }
        if (editForm.value.traffic_scale !== undefined) {
          submitData.traffic_scale = editForm.value.traffic_scale
        }
        if (editForm.value.allow_forward !== undefined) {
          submitData.allow_forward = editForm.value.allow_forward
        }
        if (editForm.value.allow_latency_test !== undefined) {
          submitData.allow_latency_test = editForm.value.allow_latency_test
        }
        if (editForm.value.allow_ipv6 !== undefined) {
          submitData.allow_ipv6 = editForm.value.allow_ipv6
        }
        if (editForm.value.forward_endpoints !== undefined) {
          submitData.forward_endpoints = editForm.value.forward_endpoints
        }
        if (editForm.value.balance_strategy !== undefined && editForm.value.forward_endpoints?.length > 1) {
          submitData.balance_strategy = editForm.value.balance_strategy
        }
        if (editForm.value.use_forward_as_tun !== undefined && editForm.value.forward_endpoints?.length >= 1) {
          submitData.use_forward_as_tun = editForm.value.use_forward_as_tun
        }
        await modifyServer(submitData)
        ElMessage.success('Server modified successfully')
        showEditDialog.value = false
        loadServers(currentPage.value, pageSize.value)
      } catch (error) {
        console.error('Error modifying server:', error)
        ElMessage.error(error.message || 'Failed to modify server')
      } finally {
        submitting.value = false
      }
    }
  } catch (error) {
    console.error('Validation error:', error)
  }
}

const loadServers = async (page = currentPage.value, size = pageSize.value) => {
  loading.value = true
  try {
    // Prepare search parameters
    const searchParams = {
      page,
      page_size: size
    }

    // Add search criteria if they exist
    if (searchForm.value.id?.trim()) {
      searchParams.id = parseInt(searchForm.value.id.trim())
    }
    if (searchForm.value.name?.trim()) {
      searchParams.name = searchForm.value.name.trim()
    }
    if (searchForm.value.ipAddr?.trim()) {
      searchParams.ip_addr = searchForm.value.ipAddr.trim()
    }
    if (searchForm.value.version && searchForm.value.version.length > 0) {
      searchParams.version = searchForm.value.version
    }
    if (searchForm.value.status && searchForm.value.status.length > 0) {
      searchParams.status = searchForm.value.status
    }

    const { data } = await getServerList(searchParams)

    // Handle paginated response
    if (data.servers && data.pagination) {
      // New paginated response format
      servers.value = data.servers
      currentPage.value = data.pagination.current_page
      pageSize.value = data.pagination.page_size
      totalItems.value = data.pagination.total_items
      totalPages.value = data.pagination.total_pages
    } else if (data?.servers) {
      // Fallback for old response format (if backend doesn't support pagination yet)
      servers.value = data.servers
      totalItems.value = data.servers.length
      totalPages.value = 1
    } else {
      servers.value = []
      totalItems.value = 0
      totalPages.value = 1
    }

    // Extract unique versions for the filter dropdown
    const versions = new Set()
    servers.value.forEach(server => {
      const version = server.version || 'Unknown'
      versions.add(version)
    })
    availableVersions.value = Array.from(versions).sort()
  } catch (error) {
    ElMessage.error('Failed to load servers')
    servers.value = []
    totalItems.value = 0
    totalPages.value = 1
  } finally {
    loading.value = false
  }
}

// Pagination handlers
const handlePageChange = (page) => {
  currentPage.value = page
  loadServers(page, pageSize.value)
}

const handlePageSizeChange = (size) => {
  pageSize.value = size
  currentPage.value = 1 // Reset to first page when changing page size
  loadServers(1, size)
}

// Search handlers
const handleSearchInput = () => {
  // Debounce search input to avoid too many API calls
  if (searchTimeout.value) {
    clearTimeout(searchTimeout.value)
  }
  searchTimeout.value = setTimeout(() => {
    handleSearch()
  }, 500)
}

const handleSearchChange = () => {
  // Immediate search for dropdown changes
  handleSearch()
}

const handleSearch = () => {
  currentPage.value = 1 // Reset to first page when searching
  loadServers(1, pageSize.value)
}

const handleClearSearch = () => {
  searchForm.value = {
    id: '',
    name: '',
    ipAddr: '',
    version: [],
    status: []
  }
  currentPage.value = 1
  loadServers(1, pageSize.value)
}

// Search UI handlers
const toggleSearchExpanded = () => {
  searchExpanded.value = !searchExpanded.value
  saveSearchExpandedState()
}

onMounted(async () => {
  loadSearchExpandedState()
  loadServers()
  await fetchForwardEndpoints()
})
</script>

<style scoped>
.server-management {
  padding: 20px;
}

.header {
  display: flex;
  justify-content: space-between;
  align-items: center;
  margin-bottom: 20px;
}

.header h2 {
  margin: 0;
  color: var(--theme-text-primary);
}

.header-actions {
  display: flex;
  gap: 12px;
  align-items: center;
}

.search-toggle-btn {
  position: relative;
  transition: all 0.3s ease;
  border-radius: 6px;
  font-weight: 500;
}

.search-toggle-btn:hover {
  transform: translateY(-1px);
  box-shadow: 0 4px 8px rgba(0, 0, 0, 0.1);
}

.search-toggle-text {
  margin-left: 4px;
  margin-right: 4px;
}

.search-container {
  margin-bottom: 20px;
  padding: 20px;
  background: var(--el-bg-color-page);
  border-radius: 8px;
  border: 1px solid var(--el-border-color-light);
  overflow: hidden;
}

.search-form {
  display: flex;
  flex-wrap: wrap;
  gap: 16px;
  align-items: end;
}

.search-form .el-form-item {
  margin-bottom: 0;
}

.search-form .el-form-item__label {
  font-weight: 500;
  color: var(--el-text-color-regular);
}

/* Dark mode compatibility for search form */
:root.dark .search-container {
  background: var(--el-bg-color);
  border-color: var(--el-border-color);
}

:root.dark .search-toggle-btn:hover {
  box-shadow: 0 4px 8px rgba(0, 0, 0, 0.2);
}

/* Responsive design for search form */
@media (max-width: 768px) {
  .search-toggle-btn {
    width: 100%;
    justify-content: center;
  }

  .search-form {
    flex-direction: column;
    align-items: stretch;
  }

  .search-form .el-form-item {
    width: 100%;
  }

  .search-form .el-input,
  .search-form .el-select {
    width: 100% !important;
  }
}

.table-card {
  border-radius: 8px;
  background-color: var(--theme-card-bg);
  border: 1px solid var(--theme-border-base);
  box-shadow: var(--theme-shadow-light);
}

.ports-info {
  display: flex;
  align-items: center;
}

.ports-count {
  cursor: pointer;
  background-color: var(--theme-fill-light) !important;
  color: var(--theme-text-regular) !important;
  border: 1px solid var(--theme-border-base) !important;
  transition: all 0.3s ease;
}

.ports-count:hover {
  background-color: var(--theme-fill-base) !important;
  border-color: var(--theme-primary) !important;
}

/* Dark mode ports count styling */
:global(.dark) .ports-count {
  background-color: var(--theme-fill-dark) !important;
  color: var(--theme-text-primary) !important;
  border: 1px solid var(--theme-border-dark) !important;
}

:global(.dark) .ports-count:hover {
  background-color: var(--theme-fill-darker) !important;
  border-color: var(--theme-primary) !important;
}

:deep(.ports-popover) {
  padding: 0;
  background-color: var(--theme-bg-primary) !important;
  border: 1px solid var(--theme-border-base) !important;
  box-shadow: var(--theme-shadow-base) !important;
}

/* Dark mode popover styling */
:global(.dark) :deep(.ports-popover) {
  background-color: var(--theme-bg-tertiary) !important;
  border: 1px solid var(--theme-border-dark) !important;
  box-shadow: var(--theme-shadow-dark) !important;
}

:global(.dark) :deep(.ports-popover .el-popper__arrow::before) {
  background-color: var(--theme-bg-tertiary) !important;
  border: 1px solid var(--theme-border-dark) !important;
}

.ports-list {
  width: 100%;
}

.ports-list-header {
  padding: 8px 12px;
  font-weight: 600;
  border-bottom: 1px solid var(--theme-border-base);
  background-color: var(--theme-fill-light);
  color: var(--theme-text-primary);
  border-radius: 4px 4px 0 0;
}

.port-item {
  padding: 8px 12px;
  font-size: 13px;
  color: var(--theme-text-regular);
  border-bottom: 1px solid var(--theme-border-light);
  background-color: var(--theme-bg-primary);
  transition: background-color 0.2s ease;
  font-weight: 500;
}

.port-item:hover {
  background-color: var(--theme-fill-extra-light);
}

.port-item:last-child {
  border-bottom: none;
}

/* Clean borderless table styling matching ForwardEndpointManagement */
:deep(.el-table) {
  border-radius: 8px;
  background-color: var(--theme-bg-primary);
  border: none;
}

:deep(.el-table th) {
  font-weight: 600;
  background-color: var(--theme-fill-light) !important;
  color: var(--theme-text-primary) !important;
  border: none !important;
}

:deep(.el-table td) {
  background-color: var(--theme-bg-primary) !important;
  color: var(--theme-text-regular) !important;
  border: none !important;
}

:deep(.el-table tr:hover td) {
  background-color: var(--theme-fill-extra-light) !important;
}

:deep(.el-table .el-table__empty-block) {
  background-color: var(--theme-bg-primary) !important;
  color: var(--theme-text-secondary) !important;
}

/* Dark mode specific table styling */
:global(.dark) :deep(.el-table th) {
  background-color: var(--theme-fill-dark) !important;
  color: var(--theme-text-primary) !important;
  border: none !important;
}

:global(.dark) :deep(.el-table td) {
  background-color: var(--theme-bg-primary) !important;
  color: var(--theme-text-regular) !important;
  border: none !important;
}

:global(.dark) :deep(.el-table tr:hover td) {
  background-color: var(--theme-fill-light) !important;
}

:global(.dark) :deep(.el-table .el-table__empty-block) {
  background-color: var(--theme-bg-primary) !important;
  color: var(--theme-text-secondary) !important;
}

:deep(.el-button.is-circle) {
  padding: 6px;
}

:deep(.el-card__body) {
  padding: 12px;
}

.server-dialog {
  :deep(.el-dialog__header) {
    margin: 0;
    padding: 20px 24px;
    border-bottom: 1px solid var(--theme-border-base);
    background-color: var(--theme-bg-primary);
  }

  :deep(.el-dialog__title) {
    font-size: 20px;
    font-weight: 500;
    color: var(--theme-text-primary);
  }

  :deep(.el-dialog__body) {
    padding: 24px;
    background-color: var(--theme-bg-primary);
  }

  :deep(.el-dialog__footer) {
    padding: 16px 24px;
    border-top: 1px solid var(--theme-border-base);
    background-color: var(--theme-bg-primary);
  }

  :deep(.el-form-item__label) {
    font-weight: 500;
    color: var(--theme-text-primary);
    margin-bottom: 8px;
    line-height: 1.2;

    &::before {
      color: var(--theme-danger);
      margin-right: 4px;
    }
  }

  :deep(.el-input__wrapper),
  :deep(.el-input-number__decrease),
  :deep(.el-input-number__increase) {
    border-radius: 6px;
  }

  :deep(.el-input__wrapper) {
    box-shadow: var(--theme-shadow-light);
    background-color: var(--theme-bg-primary);
    border-color: var(--theme-border-base);
  }

  :deep(.el-input__wrapper.is-focus) {
    box-shadow: 0 0 0 2px var(--theme-primary-light);
    border-color: var(--theme-primary);
  }

  :deep(.el-input__wrapper:hover) {
    border-color: var(--theme-border-hover);
  }
}

.port-range {
  display: flex;
  gap: 12px;
}

.port-input {
  flex: 1;
  :deep(.el-input-number) {
    width: 100%;
  }
}

.advanced-options {
  margin-top: 16px;
}

.advanced-options-header {
  display: flex;
  align-items: center;
  justify-content: space-between;
  padding: 12px 0;
  cursor: pointer;
  user-select: none;
  color: var(--theme-text-primary);
  font-weight: 500;
  font-size: 16px;
  transition: color 0.3s ease;

  &:hover {
    color: var(--theme-primary);
  }
}

.advanced-icon {
  font-size: 16px;
  color: var(--theme-text-secondary);
  transition: transform 0.2s ease, color 0.3s ease;

  &.is-active {
    transform: rotate(180deg);
    color: var(--theme-primary);
  }
}

.advanced-options-content {
  padding-top: 8px;
}

.checkbox-group {
  display: flex;
  flex-direction: column;
  gap: 12px;
  margin-top: 16px;
}

.custom-checkbox {
  :deep(.el-checkbox__label) {
    color: var(--theme-text-primary);
  }

  :deep(.el-checkbox__input.is-checked .el-checkbox__inner) {
    background-color: var(--theme-primary);
    border-color: var(--theme-primary);
  }

  :deep(.el-checkbox__inner) {
    background-color: var(--theme-bg-primary);
    border-color: var(--theme-border-base);
  }

  :deep(.el-checkbox__inner:hover) {
    border-color: var(--theme-primary);
  }
}

.dialog-footer {
  display: flex;
  justify-content: flex-end;
  gap: 12px;
}

.cancel-button {
  border-radius: 6px;
  border: 1px solid var(--theme-border-base);
  background-color: var(--theme-bg-primary);
  color: var(--theme-text-regular);
  transition: all 0.3s ease;

  &:hover {
    border-color: var(--theme-border-hover);
    background-color: var(--theme-fill-light);
    color: var(--theme-text-primary);
  }
}

.submit-button {
  border-radius: 6px;
  background-color: var(--theme-primary);
  border-color: var(--theme-primary);
  transition: all 0.3s ease;

  &:hover {
    background-color: var(--theme-primary-dark);
    border-color: var(--theme-primary-dark);
    transform: translateY(-1px);
    box-shadow: var(--theme-shadow-base);
  }
}

:deep(.el-form-item__error) {
  color: var(--theme-danger);
  font-size: 13px;
  margin-top: 4px;
}

/* Additional dark mode enhancements */
.server-management {
  background-color: var(--theme-bg-secondary);
  transition: background-color 0.3s ease;
}

/* Enhanced status tags */
:deep(.el-tag) {
  transition: all 0.3s ease;
  font-weight: 500;
}

:deep(.el-tag:hover) {
  transform: translateY(-1px);
  box-shadow: var(--theme-shadow-light);
}

/* Enhanced buttons */
:deep(.el-button) {
  transition: all 0.3s ease;
}

:deep(.el-button:hover) {
  transform: translateY(-1px);
}

:deep(.el-button.is-circle:hover) {
  box-shadow: var(--theme-shadow-base);
}

/* Enhanced dialog */
:deep(.el-dialog) {
  background-color: var(--theme-bg-primary);
  border: 1px solid var(--theme-border-base);
  box-shadow: var(--theme-shadow-modal);
}

/* Enhanced scrollbar */
:deep(.el-scrollbar__thumb) {
  background-color: var(--theme-border-base);
}

:deep(.el-scrollbar__thumb:hover) {
  background-color: var(--theme-border-hover);
}

/* Enhanced loading - now handled globally in themes.css */
:deep(.el-loading-mask) {
  background-color: var(--loading-background-overlay) !important;
  backdrop-filter: blur(2px);
}

:deep(.el-loading-spinner .path) {
  stroke: var(--loading-spinner-color) !important;
  stroke-width: var(--loading-spinner-border-width) !important;
}

:deep(.el-loading-text) {
  color: var(--loading-text-color) !important;
}

/* Enhanced select dropdown */
:deep(.el-select-dropdown) {
  background-color: var(--theme-bg-primary);
  border: 1px solid var(--theme-border-base);
  box-shadow: var(--theme-shadow-dropdown);
}

:deep(.el-select-dropdown__item) {
  color: var(--theme-text-regular);
}

:deep(.el-select-dropdown__item:hover) {
  background-color: var(--theme-fill-light);
}

:deep(.el-select-dropdown__item.selected) {
  background-color: var(--theme-primary-light);
  color: var(--theme-primary);
}

/* Pagination Styles */
.pagination-container {
  display: flex;
  justify-content: space-between;
  align-items: center;
  margin-top: 20px;
  padding: 0 20px;
}

.pagination-info {
  display: flex;
  align-items: center;
  gap: 12px;
  font-size: 14px;
  color: var(--el-text-color-regular);
}

.page-size-selector {
  width: 120px;
}

.pagination-controls {
  flex-shrink: 0;
}

@media (max-width: 768px) {
  .pagination-container {
    flex-direction: column;
    gap: 16px;
    align-items: center;
  }

  .pagination-info {
    order: 2;
  }

  .pagination-controls {
    order: 1;
  }
}
</style>
